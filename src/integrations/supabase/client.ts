// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mvyuppahdvqwvhejxxqy.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im12eXVwcGFoZHZxd3ZoZWp4eHF5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5NDI5NjgsImV4cCI6MjA2ODUxODk2OH0.qFV3_lcx1VIlB0uPmZe6h7xaBVH5PmFdf44EvUpL-to";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});