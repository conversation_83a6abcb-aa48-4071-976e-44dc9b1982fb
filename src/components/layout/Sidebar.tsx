
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Home,
  MessageCircle,
  BookOpen,
  Calendar,
  Users,
  Trophy,
  HelpCircle,
  Mail,
  Settings,
  UserPlus,
  MessagesSquare,
  GraduationCap,
  Camera,
  Gamepad2,
  Shield
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Chat & Social', href: '/chat', icon: MessageCircle, children: [
    { name: 'Campus Chat', href: '/chat' },
    { name: 'Forums', href: '/forums' },
    { name: 'Friend Finder', href: '/friends' },
  ]},
  { name: 'Study Corner', href: '/study', icon: BookOpen, children: [
    { name: 'Study Materials', href: '/study/materials' },
    { name: 'Study Groups', href: '/study/groups' },
    { name: 'Past Papers', href: '/study/papers' },
  ]},
  { name: 'Campus Life', href: '/campus', icon: GraduationCap, children: [
    { name: 'Events', href: '/events' },
    { name: 'Societies', href: '/societies' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'Initiatives', href: '/initiatives' },
  ]},
  { name: 'Fun & Games', href: '/games', icon: Gamepad2, children: [
    { name: 'Q&A Forums', href: '/qa' },
    { name: 'Entertainment', href: '/entertainment' },
  ]},
  { name: 'Support', href: '/support', icon: HelpCircle, children: [
    { name: 'FAQ', href: '/faq' },
    { name: 'Contact', href: '/contact' },
  ]},
];

export const Sidebar = () => {
  const location = useLocation();
  const { profile } = useAuth();

  return (
    <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:pt-16">
      <div className="flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="flex flex-col">
            <p className="text-sm font-medium text-gray-900">
              Welcome back, {profile?.full_name?.split(' ')[0] || 'Student'}!
            </p>
            <p className="text-xs text-gray-500">
              {profile?.college} • {profile?.course}
            </p>
          </div>
        </div>
        
        <div className="mt-5 flex-grow flex flex-col">
          <nav className="flex-1 px-2 space-y-1">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href || 
                             (item.children && item.children.some(child => location.pathname === child.href));
              
              return (
                <div key={item.name}>
                  <Link
                    to={item.href}
                    className={cn(
                      'group flex items-center px-2 py-2 text-sm font-medium rounded-md',
                      isActive
                        ? 'bg-blue-100 text-blue-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    )}
                  >
                    <item.icon
                      className={cn(
                        'mr-3 flex-shrink-0 h-5 w-5',
                        isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                      )}
                    />
                    {item.name}
                  </Link>
                  
                  {item.children && isActive && (
                    <div className="ml-8 mt-1 space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          to={child.href}
                          className={cn(
                            'block px-2 py-1 text-sm rounded-md',
                            location.pathname === child.href
                              ? 'text-blue-600 bg-blue-50'
                              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                          )}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>
          
          {/* Admin Panel Link */}
          {profile?.role === 'admin' && (
            <div className="px-2 mt-4 pt-4 border-t border-gray-200">
              <Link
                to="/admin"
                className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50"
              >
                <Shield className="mr-3 flex-shrink-0 h-5 w-5" />
                Admin Panel
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
