
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Home,
  MessageCircle,
  BookOpen,
  Calendar,
  Users,
  Trophy,
  HelpCircle,
  Mail,
  Settings,
  UserPlus,
  MessagesSquare,
  GraduationCap,
  Camera,
  Gamepad2,
  Shield
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Chat & Social', href: '/chat', icon: MessageCircle, children: [
    { name: 'Campus Chat', href: '/chat' },
    { name: 'Forums', href: '/forums' },
    { name: 'Friend Finder', href: '/friends' },
  ]},
  { name: 'Study Corner', href: '/study', icon: BookOpen, children: [
    { name: 'Study Materials', href: '/study/materials' },
    { name: 'Study Groups', href: '/study/groups' },
    { name: 'Past Papers', href: '/study/papers' },
  ]},
  { name: 'Campus Life', href: '/campus', icon: GraduationCap, children: [
    { name: 'Events', href: '/events' },
    { name: 'Societies', href: '/societies' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'Initiatives', href: '/initiatives' },
  ]},
  { name: 'Fun & Games', href: '/games', icon: Gamepad2, children: [
    { name: 'Q&A Forums', href: '/qa' },
    { name: 'Entertainment', href: '/entertainment' },
  ]},
  { name: 'Support', href: '/support', icon: HelpCircle, children: [
    { name: 'FAQ', href: '/faq' },
    { name: 'Contact', href: '/contact' },
  ]},
];

export const Sidebar = () => {
  const location = useLocation();
  const { profile } = useAuth();

  return (
    <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:pt-16">
      <div className="flex flex-col flex-grow bg-sidebar border-r border-sidebar-border pt-5 pb-4 overflow-y-auto scrollbar-thin">
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="flex flex-col">
            <p className="text-sm font-medium text-sidebar-foreground">
              Welcome back, {profile?.full_name?.split(' ')[0] || 'Student'}!
            </p>
            <p className="text-xs text-muted-foreground">
              {profile?.college} • {profile?.course}
            </p>
          </div>
        </div>
        
        <div className="mt-5 flex-grow flex flex-col">
          <nav className="flex-1 px-2 space-y-1">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href || 
                             (item.children && item.children.some(child => location.pathname === child.href));
              
              return (
                <div key={item.name}>
                  <Link
                    to={item.href}
                    className={cn(
                      'group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                      isActive
                        ? 'bg-sidebar-accent text-sidebar-primary shadow-sm'
                        : 'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-primary'
                    )}
                  >
                    <item.icon
                      className={cn(
                        'mr-3 flex-shrink-0 h-5 w-5 transition-colors',
                        isActive ? 'text-sidebar-primary' : 'text-muted-foreground group-hover:text-sidebar-primary'
                      )}
                    />
                    {item.name}
                  </Link>
                  
                  {item.children && isActive && (
                    <div className="ml-8 mt-2 space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          to={child.href}
                          className={cn(
                            'block px-3 py-1.5 text-sm rounded-md transition-colors',
                            location.pathname === child.href
                              ? 'text-sidebar-primary bg-sidebar-accent/70'
                              : 'text-muted-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent/30'
                          )}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>
          
          {/* Admin Panel Link */}
          {profile?.role === 'admin' && (
            <div className="px-2 mt-4 pt-4 border-t border-sidebar-border">
              <Link
                to="/admin"
                className="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg text-destructive hover:bg-destructive/10 transition-colors"
              >
                <Shield className="mr-3 flex-shrink-0 h-5 w-5" />
                Admin Panel
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
