
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import {
  Calendar,
  MessageCircle,
  BookOpen,
  Users,
  TrendingUp,
  Clock,
  Star,
  Award
} from 'lucide-react';

export const Dashboard = () => {
  const { profile } = useAuth();

  // Fetch recent posts
  const { data: recentPosts } = useQuery({
    queryKey: ['recent-posts'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:user_id (full_name, avatar_url),
          forums (name)
        `)
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (error) throw error;
      return data;
    },
  });

  // Fetch upcoming events
  const { data: upcomingEvents } = useQuery({
    queryKey: ['upcoming-events'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .gte('event_date', new Date().toISOString())
        .order('event_date', { ascending: true })
        .limit(3);
      
      if (error) throw error;
      return data;
    },
  });

  // Fetch study materials
  const { data: studyMaterials } = useQuery({
    queryKey: ['recent-materials'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('study_materials')
        .select('*')
        .eq('is_approved', true)
        .order('created_at', { ascending: false })
        .limit(4);
      
      if (error) throw error;
      return data;
    },
  });

  const stats = [
    {
      title: 'Reputation Score',
      value: profile?.reputation_score || 0,
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Active Chats',
      value: '3',
      icon: MessageCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Study Groups',
      value: '2',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Materials Shared',
      value: '8',
      icon: BookOpen,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  return (
    <div className="container mx-auto px-4 space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {profile?.full_name?.split(' ')[0] || 'Student'}! 🎓
        </h1>
        <p className="text-blue-100">
          Stay connected with your DU community. Check out what's happening around campus today.
        </p>
        <div className="mt-4 flex flex-wrap gap-2">
          <Badge variant="secondary" className="bg-white/20 text-white">
            {profile?.college}
          </Badge>
          <Badge variant="secondary" className="bg-white/20 text-white">
            {profile?.course}
          </Badge>
          <Badge variant="secondary" className="bg-white/20 text-white">
            Year {profile?.year}
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Forum Posts */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Trending Discussions
              </CardTitle>
              <Button variant="outline" size="sm" asChild>
                <Link to="/forums">View All</Link>
              </Button>
            </div>
            <CardDescription>Popular posts from the community</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentPosts?.map((post) => (
              <div key={post.id} className="border-b border-gray-100 pb-3 last:border-b-0">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-medium text-blue-600">
                      {post.profiles?.full_name?.charAt(0) || 'A'}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm line-clamp-1">{post.title}</h4>
                    <p className="text-xs text-gray-500 mt-1">
                      in {post.forums?.name} • {post.upvotes} upvotes
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Upcoming Events */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Upcoming Events
              </CardTitle>
              <Button variant="outline" size="sm" asChild>
                <Link to="/events">View All</Link>
              </Button>
            </div>
            <CardDescription>Don't miss out on campus activities</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingEvents?.map((event) => (
              <div key={event.id} className="border-b border-gray-100 pb-3 last:border-b-0">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Calendar className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm line-clamp-1">{event.title}</h4>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(event.event_date).toLocaleDateString()} • {event.location}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Study Materials */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="w-5 h-5" />
              Recent Study Materials
            </CardTitle>
            <Button variant="outline" size="sm" asChild>
              <Link to="/study/materials">Browse All</Link>
            </Button>
          </div>
          <CardDescription>Latest resources shared by the community</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {studyMaterials?.map((material) => (
              <div key={material.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <BookOpen className="w-5 h-5 text-purple-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm line-clamp-2">{material.title}</h4>
                    <p className="text-xs text-gray-500 mt-1">{material.subject}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {material.content_type}
                      </Badge>
                      <span className="text-xs text-gray-400">
                        ⭐ {material.rating?.toFixed(1) || '0.0'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link to="/chat" className="block">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <MessageCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Start Chatting</h3>
              <p className="text-sm text-gray-600">Connect with classmates and join group discussions</p>
            </CardContent>
          </Card>
        </Link>

        <Link to="/study/materials" className="block">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <BookOpen className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Share Materials</h3>
              <p className="text-sm text-gray-600">Upload and discover study resources</p>
            </CardContent>
          </Card>
        </Link>

        <Link to="/events" className="block">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Calendar className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Join Events</h3>
              <p className="text-sm text-gray-600">Discover and RSVP to campus activities</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
};
