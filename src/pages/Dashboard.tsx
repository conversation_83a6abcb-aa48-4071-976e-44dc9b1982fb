
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import {
  Calendar,
  MessageCircle,
  BookOpen,
  Users,
  TrendingUp,
  Star
} from 'lucide-react';

export const Dashboard = () => {
  const { profile } = useAuth();

  // Fetch recent posts
  const { data: recentPosts } = useQuery({
    queryKey: ['recent-posts'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:user_id (full_name, avatar_url),
          forums (name)
        `)
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (error) throw error;
      return data;
    },
  });

  // Fetch upcoming events
  const { data: upcomingEvents } = useQuery({
    queryKey: ['upcoming-events'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .gte('event_date', new Date().toISOString())
        .order('event_date', { ascending: true })
        .limit(3);
      
      if (error) throw error;
      return data;
    },
  });

  // Fetch study materials
  const { data: studyMaterials } = useQuery({
    queryKey: ['recent-materials'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('study_materials')
        .select('*')
        .eq('is_approved', true)
        .order('created_at', { ascending: false })
        .limit(4);
      
      if (error) throw error;
      return data;
    },
  });

  const stats = [
    {
      title: 'Reputation Score',
      value: profile?.reputation_score || 0,
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Active Chats',
      value: '3',
      icon: MessageCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Study Groups',
      value: '2',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Materials Shared',
      value: '8',
      icon: BookOpen,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  return (
    <div className="container mx-auto px-4 space-y-6">
      {/* Welcome Section */}
      <div className="gradient-primary rounded-xl p-8 text-white shadow-2xl">
        <h1 className="text-3xl font-bold mb-3">
          Welcome back, {profile?.full_name?.split(' ')[0] || 'Student'}! 🎓
        </h1>
        <p className="text-white/90 text-lg">
          Stay connected with your DU community. Check out what's happening around campus today.
        </p>
        <div className="mt-6 flex flex-wrap gap-3">
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30 hover:bg-white/30 transition-colors">
            {profile?.college}
          </Badge>
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30 hover:bg-white/30 transition-colors">
            {profile?.course}
          </Badge>
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30 hover:bg-white/30 transition-colors">
            Year {profile?.year}
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title} className="card-modern hover:scale-105 transition-transform duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-3xl font-bold text-foreground mt-1">{stat.value}</p>
                </div>
                <div className={`p-4 rounded-xl ${stat.bgColor} shadow-lg`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Forum Posts */}
        <Card className="card-modern">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-foreground">
                <TrendingUp className="w-5 h-5 text-primary" />
                Trending Discussions
              </CardTitle>
              <Button variant="outline" size="sm" asChild className="hover:bg-accent/50">
                <Link to="/forums">View All</Link>
              </Button>
            </div>
            <CardDescription className="text-muted-foreground">Popular posts from the community</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentPosts?.map((post) => (
              <div key={post.id} className="border-b border-border pb-4 last:border-b-0 hover:bg-accent/20 rounded-lg p-2 transition-colors">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 bg-accent rounded-full flex items-center justify-center flex-shrink-0 ring-2 ring-primary/20">
                    <span className="text-sm font-medium text-primary">
                      {post.profiles?.full_name?.charAt(0) || 'A'}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm line-clamp-1 text-foreground">{post.title}</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      in {post.forums?.name} • {post.upvotes} upvotes
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Upcoming Events */}
        <Card className="card-modern">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-foreground">
                <Calendar className="w-5 h-5 text-primary" />
                Upcoming Events
              </CardTitle>
              <Button variant="outline" size="sm" asChild className="hover:bg-accent/50">
                <Link to="/events">View All</Link>
              </Button>
            </div>
            <CardDescription className="text-muted-foreground">Don't miss out on campus activities</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingEvents?.map((event) => (
              <div key={event.id} className="border-b border-border pb-4 last:border-b-0 hover:bg-accent/20 rounded-lg p-2 transition-colors">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0 ring-2 ring-green-500/30">
                    <Calendar className="w-5 h-5 text-green-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm line-clamp-1 text-foreground">{event.title}</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      {new Date(event.event_date).toLocaleDateString()} • {event.location}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Study Materials */}
      <Card className="card-modern">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-foreground">
              <BookOpen className="w-5 h-5 text-primary" />
              Recent Study Materials
            </CardTitle>
            <Button variant="outline" size="sm" asChild className="hover:bg-accent/50">
              <Link to="/study/materials">Browse All</Link>
            </Button>
          </div>
          <CardDescription className="text-muted-foreground">Latest resources shared by the community</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {studyMaterials?.map((material) => (
              <div key={material.id} className="border border-border rounded-xl p-4 hover:shadow-xl hover:scale-105 transition-all duration-300 bg-card">
                <div className="flex items-start gap-3">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center flex-shrink-0 ring-2 ring-purple-500/30">
                    <BookOpen className="w-6 h-6 text-purple-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm line-clamp-2 text-foreground">{material.title}</h4>
                    <p className="text-xs text-muted-foreground mt-1">{material.subject}</p>
                    <div className="flex items-center gap-2 mt-3">
                      <Badge variant="secondary" className="text-xs bg-accent/50">
                        {material.content_type}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        ⭐ {material.rating?.toFixed(1) || '0.0'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link to="/chat" className="block group">
          <Card className="card-modern hover:scale-105 transition-all duration-300 cursor-pointer group-hover:shadow-2xl">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-500/30 transition-colors">
                <MessageCircle className="w-8 h-8 text-blue-400" />
              </div>
              <h3 className="font-semibold mb-2 text-foreground">Start Chatting</h3>
              <p className="text-sm text-muted-foreground">Connect with classmates and join group discussions</p>
            </CardContent>
          </Card>
        </Link>

        <Link to="/study/materials" className="block group">
          <Card className="card-modern hover:scale-105 transition-all duration-300 cursor-pointer group-hover:shadow-2xl">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-green-500/30 transition-colors">
                <BookOpen className="w-8 h-8 text-green-400" />
              </div>
              <h3 className="font-semibold mb-2 text-foreground">Share Materials</h3>
              <p className="text-sm text-muted-foreground">Upload and discover study resources</p>
            </CardContent>
          </Card>
        </Link>

        <Link to="/events" className="block group">
          <Card className="card-modern hover:scale-105 transition-all duration-300 cursor-pointer group-hover:shadow-2xl">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-500/30 transition-colors">
                <Calendar className="w-8 h-8 text-purple-400" />
              </div>
              <h3 className="font-semibold mb-2 text-foreground">Join Events</h3>
              <p className="text-sm text-muted-foreground">Discover and RSVP to campus activities</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
};
