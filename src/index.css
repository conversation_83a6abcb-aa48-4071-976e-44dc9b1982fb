@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Light theme colors - keeping as fallback */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Default to dark theme - Professional DU Platform Dark Mode */
  :root {
    /* Primary dark navy - #1a1a2e */
    --background: 225 47% 9%;
    --foreground: 210 40% 98%;

    /* Card backgrounds - slightly lighter than main background */
    --card: 225 39% 13%;
    --card-foreground: 210 40% 98%;

    /* Popover backgrounds */
    --popover: 225 39% 13%;
    --popover-foreground: 210 40% 98%;

    /* Primary accent - deep blue #0f3460 */
    --primary: 210 75% 22%;
    --primary-foreground: 210 40% 98%;

    /* Secondary - darker blue #16213e */
    --secondary: 225 42% 16%;
    --secondary-foreground: 210 40% 98%;

    /* Muted backgrounds and text */
    --muted: 225 39% 13%;
    --muted-foreground: 215 20.2% 65.1%;

    /* Accent colors */
    --accent: 210 75% 22%;
    --accent-foreground: 210 40% 98%;

    /* Destructive colors */
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 210 40% 98%;

    /* Borders and inputs */
    --border: 225 39% 18%;
    --input: 225 39% 18%;
    --ring: 210 75% 22%;

    /* Sidebar colors */
    --sidebar-background: 225 47% 9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 75% 22%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 225 39% 13%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 225 39% 18%;
    --sidebar-ring: 210 75% 22%;
  }

  /* Light theme override */
  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 75% 22%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 75% 22%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 210 75% 22%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 210 75% 22%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  /* Smooth transitions for theme switching */
  * {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }
}

@layer components {
  /* Glass-morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }

  /* Modern card styles */
  .card-modern {
    @apply bg-card border border-border rounded-lg shadow-lg hover:shadow-xl transition-all duration-300;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(210 75% 35%) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(225 42% 25%) 100%);
  }

  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--foreground));
  }
}