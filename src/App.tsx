
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { Dashboard } from "@/pages/Dashboard";
import { Login } from "@/pages/Login";
import { Signup } from "@/pages/Signup";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route 
              path="/" 
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } 
            />
            {/* Placeholder routes for future development */}
            <Route 
              path="/chat" 
              element={
                <ProtectedRoute>
                  <div className="container mx-auto px-4 py-8">
                    <h1 className="text-2xl font-bold">Chat & Social - Coming Soon!</h1>
                    <p className="text-gray-600 mt-2">Real-time messaging and social features will be available here.</p>
                  </div>
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/study/*" 
              element={
                <ProtectedRoute>
                  <div className="container mx-auto px-4 py-8">
                    <h1 className="text-2xl font-bold">Study Corner - Coming Soon!</h1>
                    <p className="text-gray-600 mt-2">Study materials, groups, and collaboration tools will be available here.</p>
                  </div>
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/events" 
              element={
                <ProtectedRoute>
                  <div className="container mx-auto px-4 py-8">
                    <h1 className="text-2xl font-bold">Events - Coming Soon!</h1>
                    <p className="text-gray-600 mt-2">Campus events and RSVP functionality will be available here.</p>
                  </div>
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/societies" 
              element={
                <ProtectedRoute>
                  <div className="container mx-auto px-4 py-8">
                    <h1 className="text-2xl font-bold">Societies - Coming Soon!</h1>
                    <p className="text-gray-600 mt-2">Society pages and club management will be available here.</p>
                  </div>
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/forums" 
              element={
                <ProtectedRoute>
                  <div className="container mx-auto px-4 py-8">
                    <h1 className="text-2xl font-bold">Forums - Coming Soon!</h1>
                    <p className="text-gray-600 mt-2">Discussion forums and Q&A will be available here.</p>
                  </div>
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/admin" 
              element={
                <ProtectedRoute requiredRole="admin">
                  <div className="container mx-auto px-4 py-8">
                    <h1 className="text-2xl font-bold">Admin Panel - Coming Soon!</h1>
                    <p className="text-gray-600 mt-2">Administrative tools and content moderation will be available here.</p>
                  </div>
                </ProtectedRoute>
              } 
            />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
