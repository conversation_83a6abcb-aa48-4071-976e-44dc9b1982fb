
-- Drop the problematic RLS policy
DROP POLICY IF EXISTS "Users can view public profiles" ON public.profiles;

-- Create a security definer function to get current user role safely
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT AS $$
  SELECT role::text FROM public.profiles WHERE user_id = auth.uid();
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- Create a simpler, non-recursive policy for profiles
CREATE POLICY "Users can view public profiles" ON public.profiles
  FOR SELECT USING (
    privacy_settings->>'profile' = 'public' OR 
    user_id = auth.uid()
  );
